# 服务监听地址
listen_addr: ":3100"

# 上游服务列表
upstreams:
  - name: "vllm"
    # 上游的基础 URL，代理会将请求路径附加到这个 URL 后面
    base_url: "http://172.16.8.228:9000"
    # 注意：这是上游服务需要的 API Key，而不是我们代理服务的 Key
    api_key: "sk-your-openai-api-key"
    # 这个上游负责处理的模型列表
    models:
      - "qwen2.5-14b"
    # 模型映射：将客户端请求的模型名称映射到上游实际支持的模型名称
    # 格式：客户端模型名: 上游模型名
    model_mapping:
      "qwen2.5": "qwen2.5-14b"

  - name: "DeepSeek"
    base_url: "http://new-api.easemob.top:3000"
    api_key: "sk-Srby6jVTks35T9xUbezYJN1ErvKFVOPpDh8QOwq0xLHstY1r"
    models:
      - "deepseek-chat"
      - "deepseek-reasoner"

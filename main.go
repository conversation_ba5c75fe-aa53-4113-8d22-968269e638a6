package main

import (
	"bytes"
	_ "context"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	_ "net/http/httputil"
	"os"
	"strings"
	"time"

	"github.com/tidwall/gjson"
	"gopkg.in/yaml.v3"
)

// --- Configuration Structures ---

type UpstreamConfig struct {
	Name         string            `yaml:"name"`
	BaseURL      string            `yaml:"base_url"`
	APIKey       string            `yaml:"api_key"`
	Models       []string          `yaml:"models"`
	ModelMapping map[string]string `yaml:"model_mapping"` // 客户端模型名 -> 上游模型名的映射
}

type Config struct {
	ListenAddr string           `yaml:"listen_addr"`
	Upstreams  []UpstreamConfig `yaml:"upstreams"`
}

// --- Proxy Handler ---

type ProxyHandler struct {
	// modelToUpstream 是一个从客户端请求的模型名称到上游配置的映射，用于 O(1) 查找
	modelToUpstream map[string]*UpstreamConfig
	// modelMapping 是一个从客户端模型名称到上游实际模型名称的映射
	modelMapping map[string]string
	// client 是一个全局共享的、经过优化的 HTTP 客户端
	client *http.Client
}

func NewProxyHandler(config *Config) (*ProxyHandler, error) {
	// 创建一个高效的 HTTP Client
	// 这个 Client 会被所有请求复用，以重用 TCP 连接
	client := &http.Client{
		Transport: &http.Transport{
			Proxy: http.ProxyFromEnvironment,
			DialContext: (&net.Dialer{
				Timeout:   30 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			ForceAttemptHTTP2:     true,
			MaxIdleConns:          100, // 增加最大空闲连接数
			MaxIdleConnsPerHost:   10,  // 增加每个主机的最大空闲连接数
			IdleConnTimeout:       90 * time.Second,
			TLSHandshakeTimeout:   10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
		},
		// 设置一个合理的超时时间，防止请求永久挂起
		Timeout: 5 * time.Minute,
	}

	// 构建 model -> upstream 的映射和模型映射表
	modelToUpstream := make(map[string]*UpstreamConfig)
	modelMapping := make(map[string]string)

	for i := range config.Upstreams {
		upstream := &config.Upstreams[i] // 使用指针避免循环变量问题

		// 如果没有配置 model_mapping，则为每个模型创建直接映射
		if upstream.ModelMapping == nil {
			upstream.ModelMapping = make(map[string]string)
			for _, model := range upstream.Models {
				upstream.ModelMapping[model] = model
			}
		}

		// 构建客户端模型名到上游的映射
		for clientModel, upstreamModel := range upstream.ModelMapping {
			if _, exists := modelToUpstream[clientModel]; exists {
				return nil, fmt.Errorf("client model '%s' is defined in multiple upstreams", clientModel)
			}

			// 验证上游模型是否在该上游的模型列表中
			found := false
			for _, supportedModel := range upstream.Models {
				if supportedModel == upstreamModel {
					found = true
					break
				}
			}
			if !found {
				return nil, fmt.Errorf("upstream model '%s' is not in the models list for upstream '%s'", upstreamModel, upstream.Name)
			}

			modelToUpstream[clientModel] = upstream
			modelMapping[clientModel] = upstreamModel
			log.Printf("Routing client model '%s' to upstream '%s' with upstream model '%s'", clientModel, upstream.Name, upstreamModel)
		}
	}

	return &ProxyHandler{
		modelToUpstream: modelToUpstream,
		modelMapping:    modelMapping,
		client:          client,
	}, nil
}

func (h *ProxyHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// --- 1. 读取并解析请求体 ---
	// 我们需要先读取请求体以获取 'model'，然后再将其转发给上游。
	// io.ReadAll 是最高效的方式，对于典型的 API 请求大小，性能开销很小。
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Failed to read request body", http.StatusInternalServerError)
		log.Printf("Error reading body: %v", err)
		return
	}
	// 立即关闭原始请求体
	r.Body.Close()

	// 使用 gjson 高效地提取 model 字段，避免完全 unmarshal
	modelResult := gjson.GetBytes(body, "model")
	if !modelResult.Exists() {
		http.Error(w, "Request body must contain a 'model' field", http.StatusBadRequest)
		return
	}
	model := modelResult.String()

	// --- 2. 查找上游 ---
	upstream, ok := h.modelToUpstream[model]
	if !ok {
		http.Error(w, fmt.Sprintf("No upstream configured for model '%s'", model), http.StatusBadRequest)
		return
	}

	// --- 3. 创建并发送上游请求 ---
	// 拼接目标 URL
	targetURL := upstream.BaseURL + r.URL.Path

	// 创建一个新的请求，并将原始请求的上下文传递过去，以支持超时和取消
	proxyReq, err := http.NewRequestWithContext(r.Context(), r.Method, targetURL, bytes.NewReader(body))
	if err != nil {
		http.Error(w, "Failed to create proxy request", http.StatusInternalServerError)
		log.Printf("Error creating proxy request: %v", err)
		return
	}

	// 复制原始请求头，但排除一些代理不应转发的头
	copyHeaders(proxyReq.Header, r.Header)

	// 设置上游需要的认证头
	proxyReq.Header.Set("Authorization", "Bearer "+upstream.APIKey)

	// 执行请求
	resp, err := h.client.Do(proxyReq)
	if err != nil {
		http.Error(w, "Failed to send request to upstream", http.StatusBadGateway)
		log.Printf("Error forwarding request to upstream '%s': %v", upstream.Name, err)
		return
	}
	defer resp.Body.Close()

	// --- 4. 将上游响应写回客户端 ---
	// 复制上游响应头到我们的响应中
	copyHeaders(w.Header(), resp.Header)
	// 写入响应状态码
	w.WriteHeader(resp.StatusCode)

	// **关键：处理流式响应 (SSE)**
	// 如果是流式响应，我们需要边接收边刷新到客户端
	if isStreaming(resp) {
		// 获取 Flusher 接口，用于强制刷新数据到客户端
		flusher, ok := w.(http.Flusher)
		if !ok {
			// 这种情况很少见，但为了健壮性需要处理
			http.Error(w, "Streaming not supported by the connection", http.StatusInternalServerError)
			return
		}

		// 使用一个小的缓冲区进行 io.Copy，并在每次写入后 Flush
		buf := make([]byte, 32*1024) // 32KB buffer
		for {
			n, err := resp.Body.Read(buf)
			if n > 0 {
				if _, writeErr := w.Write(buf[:n]); writeErr != nil {
					log.Printf("Error writing to client: %v", writeErr)
					break
				}
				flusher.Flush() // 关键步骤：立即将数据发送给客户端
			}
			if err == io.EOF {
				break
			}
			if err != nil {
				log.Printf("Error reading from upstream: %v", err)
				break
			}
		}
	} else {
		// 对于非流式响应，直接用 io.Copy 即可
		if _, err := io.Copy(w, resp.Body); err != nil {
			log.Printf("Error copying response body to client: %v", err)
		}
	}
}

// 辅助函数：复制头信息
func copyHeaders(dst, src http.Header) {
	for k, vv := range src {
		// 跳过一些不应被代理的头
		if k == "Content-Length" || k == "Connection" || k == "Te" || k == "Transfer-Encoding" || k == "Upgrade" {
			continue
		}
		for _, v := range vv {
			dst.Add(k, v)
		}
	}
}

// 辅助函数：判断是否为流式响应
func isStreaming(resp *http.Response) bool {
	return strings.Contains(resp.Header.Get("Content-Type"), "text/event-stream")
}

// --- Main Function ---

func main() {
	// 读取配置文件
	configFile, err := os.ReadFile("config.yaml")
	if err != nil {
		log.Fatalf("Failed to read config file: %v", err)
	}

	var config Config
	if err := yaml.Unmarshal(configFile, &config); err != nil {
		log.Fatalf("Failed to parse config file: %v", err)
	}

	// 创建代理处理器
	proxy, err := NewProxyHandler(&config)
	if err != nil {
		log.Fatalf("Failed to create proxy handler: %v", err)
	}

	// 创建一个只处理特定 OpenAI 端点的 mux
	// 这样可以避免代理不必要的路径
	mux := http.NewServeMux()
	mux.Handle("/v1/chat/completions", proxy)
	// 如果需要代理其他端点，可以在这里添加
	// mux.Handle("/v1/embeddings", proxy)
	// ...

	// 创建并配置服务器
	server := &http.Server{
		Addr:    config.ListenAddr,
		Handler: mux,
		// 设置一些合理的超时来提高服务器的鲁棒性
		ReadTimeout:  5 * time.Second,
		WriteTimeout: 5 * time.Minute, // 写入超时要长，以支持长时间的流式响应
		IdleTimeout:  120 * time.Second,
	}

	log.Printf("OpenAI API Proxy is running on %s", config.ListenAddr)
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("Could not listen on %s: %v\n", config.ListenAddr, err)
	}
}
